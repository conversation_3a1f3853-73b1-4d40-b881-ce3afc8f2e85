-- Add engagement tracking fields to conversations table
ALTER TABLE forachat.conversations 
ADD COLUMN last_user_activity TIMESTAMP,
ADD COLUMN engagement_level DECIMAL(3,2) DEFAULT 1.0;

-- Add index for performance on last_user_activity queries
CREATE INDEX idx_conversations_last_user_activity ON forachat.conversations(last_user_activity);

-- Update existing conversations to have initial values
UPDATE forachat.conversations 
SET 
  last_user_activity = updated_at,
  engagement_level = 1.0
WHERE last_user_activity IS NULL;
